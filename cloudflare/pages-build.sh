#!/bin/bash

# Cloudflare Pages 构建脚本
# 用于自动化部署流程

echo "🚀 开始 Cloudflare Pages 构建..."

# 设置环境变量
export NODE_ENV=production
export NEXT_TELEMETRY_DISABLED=1

# 安装依赖
echo "📦 安装依赖..."
npm ci --production=false

# 构建项目
echo "🔨 构建 Next.js 项目..."
npm run build

# 检查构建结果
if [ $? -eq 0 ]; then
    echo "✅ 构建成功！"
    
    # 显示构建统计
    echo "📊 构建统计："
    du -sh .next/
    find .next/static -name "*.js" -o -name "*.css" | wc -l | xargs echo "静态文件数量："
    
else
    echo "❌ 构建失败！"
    exit 1
fi

echo "🎉 Cloudflare Pages 构建完成！"