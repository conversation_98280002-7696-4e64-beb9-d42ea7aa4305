# Cloudflare Pages 和 Workers 配置文件

name = "sprunkivaporwave-online"
compatibility_date = "2024-01-01"

# Pages 配置
[env.production]
name = "sprunkivaporwave-online"
compatibility_date = "2024-01-01"

# R2 存储桶绑定
[[env.production.r2_buckets]]
binding = "ASSETS_BUCKET"
bucket_name = "sprunkivaporwave-assets"

# KV 存储绑定（用于缓存）
[[env.production.kv_namespaces]]
binding = "CACHE_KV"
id = "your-kv-namespace-id"

# 环境变量
[env.production.vars]
ENVIRONMENT = "production"
NEXT_PUBLIC_R2_DOMAIN = "https://cdn.sprunkivaporwave.online"

# 开发环境配置
[env.development]
name = "sprunkivaporwave-online-dev"
compatibility_date = "2024-01-01"

[[env.development.r2_buckets]]
binding = "ASSETS_BUCKET"
bucket_name = "sprunkivaporwave-assets-dev"

[env.development.vars]
ENVIRONMENT = "development"
NEXT_PUBLIC_R2_DOMAIN = "https://cdn-dev.sprunkivaporwave.online"