/**
 * Cloudflare 工具函数
 * 处理 R2 存储、CDN 和 Pages 相关功能
 */

// Cloudflare R2 配置
export const CLOUDFLARE_CONFIG = {
  // R2 存储桶配置
  R2_DOMAIN: process.env.NEXT_PUBLIC_R2_DOMAIN || 'https://cdn.sprunkivaporwave.online',
  R2_BUCKET: process.env.CLOUDFLARE_R2_BUCKET || 'sprunkivaporwave-assets',
  
  // CDN 配置
  CDN_CACHE_TTL: {
    IMAGES: 31536000,      // 图片缓存 1 年
    FONTS: 31536000,       // 字体缓存 1 年
    STATIC: 2592000,       // 静态资源缓存 30 天
    HTML: 3600,            // HTML 缓存 1 小时
  },
  
  // 图片优化配置
  IMAGE_FORMATS: ['webp', 'avif', 'png', 'jpg'] as const,
  IMAGE_SIZES: [16, 32, 48, 64, 96, 128, 256, 384, 640, 750, 828, 1080, 1200, 1920] as const,
} as const;

/**
 * 生成 R2 资源 URL
 * @param path - 资源路径
 * @param options - 可选参数
 */
export function getR2AssetUrl(path: string, options?: {
  width?: number;
  height?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg';
  quality?: number;
}): string {
  // 移除开头的斜杠
  const cleanPath = path.startsWith('/') ? path.slice(1) : path;
  
  // 获取项目路径前缀
  const projectPath = process.env.NEXT_PUBLIC_R2_PROJECT_PATH || 'sprunkivaporwave';
  
  let url = `${CLOUDFLARE_CONFIG.R2_DOMAIN}/${projectPath}/${cleanPath}`;
  
  // 如果有图片优化参数，添加到 URL
  if (options && (options.width || options.height || options.format || options.quality)) {
    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.format) params.set('f', options.format);
    if (options.quality) params.set('q', options.quality.toString());
    
    url += `?${params.toString()}`;
  }
  
  return url;
}

/**
 * 生成响应式图片 srcSet
 * @param basePath - 基础图片路径
 * @param sizes - 图片尺寸数组
 */
export function generateSrcSet(basePath: string, sizes: number[] = [640, 750, 828, 1080, 1200, 1920]): string {
  return sizes
    .map(size => `${getR2AssetUrl(basePath, { width: size, format: 'webp' })} ${size}w`)
    .join(', ');
}

/**
 * 检查是否为生产环境
 */
export function isProduction(): boolean {
  return process.env.NODE_ENV === 'production';
}

/**
 * 获取缓存控制头
 * @param type - 资源类型
 */
export function getCacheControl(type: 'images' | 'fonts' | 'static' | 'html'): string {
  const ttl = CLOUDFLARE_CONFIG.CDN_CACHE_TTL[type.toUpperCase() as keyof typeof CLOUDFLARE_CONFIG.CDN_CACHE_TTL];
  
  if (type === 'html') {
    return `public, max-age=${ttl}, s-maxage=86400, stale-while-revalidate=3600`;
  }
  
  return `public, max-age=${ttl}, immutable`;
}

/**
 * 预加载关键资源
 * @param resources - 资源列表
 */
export function preloadResources(resources: Array<{ href: string; as: string; type?: string }>): string {
  return resources
    .map(resource => {
      const typeAttr = resource.type ? ` type="${resource.type}"` : '';
      return `<${resource.href}>; rel=preload; as=${resource.as}${typeAttr}`;
    })
    .join(', ');
}