/**
 * Cloudflare R2 图片加载器
 * 用于 Next.js 图片优化
 */

export default function cloudflareLoader({ src, width, quality }) {
  // 如果是外部 URL，直接返回
  if (src.startsWith('http')) {
    return src;
  }
  
  // R2 CDN 域名和项目路径
  const R2_DOMAIN = process.env.NEXT_PUBLIC_R2_DOMAIN || 'https://assets.sprunkivaporwave.online';
  const PROJECT_PATH = process.env.NEXT_PUBLIC_R2_PROJECT_PATH || 'sprunkivaporwave';
  
  // 构建优化参数
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (quality) params.set('q', quality.toString());
  
  // 移除开头的斜杠
  const cleanSrc = src.startsWith('/') ? src.slice(1) : src;
  
  // 构建完整 URL，包含项目路径
  const url = `${R2_DOMAIN}/${PROJECT_PATH}/${cleanSrc}`;
  
  return params.toString() ? `${url}?${params.toString()}` : url;
}