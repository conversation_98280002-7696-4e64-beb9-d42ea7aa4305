# Cloudflare 部署环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 基础配置
NEXT_PUBLIC_SITE_URL=https://sprunkivaporwave.online
NEXT_PUBLIC_SITE_NAME=Sprunki Vaporwave Online

# Cloudflare R2 存储配置
NEXT_PUBLIC_R2_DOMAIN=https://cdn.sprunkivaporwave.online
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
CLOUDFLARE_R2_BUCKET=sprunkivaporwave-assets
CLOUDFLARE_R2_ACCESS_KEY_ID=your_r2_access_key_id
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_r2_secret_access_key

# Cloudflare Pages 配置
CLOUDFLARE_PAGES_PROJECT=sprunkivaporwave-online
CLOUDFLARE_ZONE_ID=your_zone_id

# 分析和监控
NEXT_PUBLIC_GOOGLE_ID=your_google_analytics_id
NEXT_PUBLIC_CLARITY_ID=your_clarity_id

# API 配置
ZHIPU_API_KEY=your_zhipu_api_key

# 构建配置
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1

# 缓存配置
ENABLE_CACHE=true
CACHE_TTL=3600

# 性能监控
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_ERROR_REPORTING=true