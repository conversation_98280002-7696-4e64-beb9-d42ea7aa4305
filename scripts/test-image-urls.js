/**
 * 测试图片 URL 生成
 */

// 模拟环境变量
process.env.NEXT_PUBLIC_R2_DOMAIN = 'https://assets.sprunkivaporwave.online';
process.env.NEXT_PUBLIC_R2_PROJECT_PATH = 'sprunkivaporwave';

// 导入图片加载器
const cloudflareLoader = require('../lib/utils/image-loader.js').default;

console.log('🔍 测试图片 URL 生成...');

// 测试用例
const testCases = [
  {
    name: '游戏缩略图',
    src: '/images/thumbnails/pokemon-gamma-emerald.png',
    width: 400,
    quality: 90
  },
  {
    name: '游戏截图',
    src: '/images/thumbnails/pokemon-gamma-emerald-img.png',
    width: 600,
    quality: 85
  },
  {
    name: '控制指南',
    src: '/images/thumbnails/pokemon-gammae-merald-banner-controls.png',
    width: 800,
    quality: 80
  },
  {
    name: 'Favicon',
    src: '/favicon.ico',
    width: 32
  },
  {
    name: 'OG 图片',
    src: '/og.png',
    width: 1200,
    quality: 85
  }
];

console.log('\n📋 生成的 URL：');
testCases.forEach(testCase => {
  const url = cloudflareLoader({
    src: testCase.src,
    width: testCase.width,
    quality: testCase.quality
  });
  
  console.log(`\n${testCase.name}:`);
  console.log(`  原始路径: ${testCase.src}`);
  console.log(`  生成URL: ${url}`);
});

console.log('\n✅ URL 生成测试完成！');
console.log('\n💡 在生产环境中，所有图片将从这些 URL 加载。');
console.log('💡 在开发环境中，图片仍然从本地 public 目录加载。');