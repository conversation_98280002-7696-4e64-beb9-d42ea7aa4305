/**
 * 测试环境变量是否正确加载
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

console.log('🔍 检查环境变量...');
console.log('CLOUDFLARE_ACCOUNT_ID:', process.env.CLOUDFLARE_ACCOUNT_ID ? '✅ 已设置' : '❌ 未设置');
console.log('CLOUDFLARE_R2_BUCKET:', process.env.CLOUDFLARE_R2_BUCKET ? '✅ 已设置' : '❌ 未设置');
console.log('CLOUDFLARE_R2_ACCESS_KEY_ID:', process.env.CLOUDFLARE_R2_ACCESS_KEY_ID ? '✅ 已设置' : '❌ 未设置');
console.log('CLOUDFLARE_R2_SECRET_ACCESS_KEY:', process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ? '✅ 已设置' : '❌ 未设置');
console.log('NEXT_PUBLIC_R2_PROJECT_PATH:', process.env.NEXT_PUBLIC_R2_PROJECT_PATH ? '✅ 已设置' : '❌ 未设置');

console.log('\n📋 环境变量值:');
console.log('Account ID:', process.env.CLOUDFLARE_ACCOUNT_ID);
console.log('Bucket:', process.env.CLOUDFLARE_R2_BUCKET);
console.log('Project Path:', process.env.NEXT_PUBLIC_R2_PROJECT_PATH);