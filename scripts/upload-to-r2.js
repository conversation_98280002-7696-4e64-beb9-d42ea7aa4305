/**
 * 上传静态资源到 Cloudflare R2
 * 自动化资源迁移脚本
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const fs = require('fs');
const path = require('path');
const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const mime = require('mime-types');

// Cloudflare R2 配置
const R2_CONFIG = {
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  region: 'auto',
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
};

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET || 'sprunkivaporwave-assets';

// 创建 S3 客户端（R2 兼容 S3 API）
const s3Client = new S3Client(R2_CONFIG);

/**
 * 检查文件是否已存在
 */
async function fileExists(key) {
  try {
    await s3Client.send(new HeadObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
    }));
    return true;
  } catch (error) {
    if (error.name === 'NotFound') {
      return false;
    }
    throw error;
  }
}

/**
 * 上传单个文件到 R2
 */
async function uploadFile(filePath, key) {
  try {
    // 检查文件是否已存在
    if (await fileExists(key)) {
      console.log(`⏭️  跳过已存在的文件: ${key}`);
      return;
    }

    // 读取文件
    const fileContent = fs.readFileSync(filePath);
    const contentType = mime.lookup(filePath) || 'application/octet-stream';

    // 设置缓存控制
    let cacheControl = 'public, max-age=31536000, immutable'; // 默认 1 年
    if (contentType.startsWith('text/html')) {
      cacheControl = 'public, max-age=3600, s-maxage=86400'; // HTML 1 小时
    }

    // 上传到 R2
    const command = new PutObjectCommand({
      Bucket: BUCKET_NAME,
      Key: key,
      Body: fileContent,
      ContentType: contentType,
      CacheControl: cacheControl,
      Metadata: {
        'uploaded-at': new Date().toISOString(),
        'original-path': filePath,
      },
    });

    await s3Client.send(command);
    console.log(`✅ 上传成功: ${key} (${contentType})`);
  } catch (error) {
    console.error(`❌ 上传失败: ${key}`, error.message);
    throw error;
  }
}

/**
 * 递归上传目录
 */
async function uploadDirectory(dirPath, prefix = '') {
  const items = fs.readdirSync(dirPath);
  
  // 获取项目路径前缀
  const projectPath = process.env.NEXT_PUBLIC_R2_PROJECT_PATH || 'sprunkivaporwave';
  
  for (const item of items) {
    const itemPath = path.join(dirPath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      await uploadDirectory(itemPath, path.join(prefix, item));
    } else {
      // 上传文件，添加项目路径前缀
      const key = path.join(projectPath, prefix, item).replace(/\\/g, '/'); // 确保使用正斜杠
      await uploadFile(itemPath, key);
    }
  }
}

/**
 * 主函数
 */
async function main() {
  console.log('🚀 开始上传静态资源到 Cloudflare R2...');
  
  // 检查环境变量
  if (!process.env.CLOUDFLARE_ACCOUNT_ID || 
      !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || 
      !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
    console.error('❌ 缺少必要的环境变量，请检查：');
    console.error('   - CLOUDFLARE_ACCOUNT_ID');
    console.error('   - CLOUDFLARE_R2_ACCESS_KEY_ID');
    console.error('   - CLOUDFLARE_R2_SECRET_ACCESS_KEY');
    process.exit(1);
  }

  try {
    // 上传 public 目录下的所有文件
    const publicDir = path.join(process.cwd(), 'public');
    
    if (fs.existsSync(publicDir)) {
      console.log(`📁 上传目录: ${publicDir}`);
      await uploadDirectory(publicDir);
    } else {
      console.log('⚠️  public 目录不存在');
    }

    console.log('🎉 所有文件上传完成！');
    
    // 显示访问信息
    console.log('\n📋 访问信息：');
    console.log(`   R2 存储桶: ${BUCKET_NAME}`);
    console.log(`   CDN 域名: ${process.env.NEXT_PUBLIC_R2_DOMAIN || 'https://cdn.sprunkivaporwave.online'}`);
    
  } catch (error) {
    console.error('❌ 上传过程中发生错误:', error);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { uploadFile, uploadDirectory };