/**
 * 调试版本的上传脚本
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

console.log('🔍 调试信息：');
console.log('Account ID:', process.env.CLOUDFLARE_ACCOUNT_ID);
console.log('Bucket:', process.env.CLOUDFLARE_R2_BUCKET);
console.log('Access Key ID:', process.env.CLOUDFLARE_R2_ACCESS_KEY_ID ? '已设置' : '未设置');
console.log('Secret Key:', process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ? '已设置' : '未设置');

const fs = require('fs');
const path = require('path');

// 检查环境变量
if (!process.env.CLOUDFLARE_ACCOUNT_ID || 
    !process.env.CLOUDFLARE_R2_ACCESS_KEY_ID || 
    !process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY) {
  console.error('❌ 缺少必要的环境变量，请检查：');
  console.error('   - CLOUDFLARE_ACCOUNT_ID:', process.env.CLOUDFLARE_ACCOUNT_ID ? '✅' : '❌');
  console.error('   - CLOUDFLARE_R2_ACCESS_KEY_ID:', process.env.CLOUDFLARE_R2_ACCESS_KEY_ID ? '✅' : '❌');
  console.error('   - CLOUDFLARE_R2_SECRET_ACCESS_KEY:', process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY ? '✅' : '❌');
  process.exit(1);
}

console.log('✅ 环境变量检查通过，开始上传...');

const { S3Client, PutObjectCommand, HeadObjectCommand } = require('@aws-sdk/client-s3');
const mime = require('mime-types');

// Cloudflare R2 配置
const R2_CONFIG = {
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  region: 'auto',
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
};

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET || 'games-igm';

console.log('📋 R2 配置：');
console.log('Endpoint:', R2_CONFIG.endpoint);
console.log('Bucket:', BUCKET_NAME);

// 创建 S3 客户端（R2 兼容 S3 API）
const s3Client = new S3Client(R2_CONFIG);

/**
 * 简单的上传测试
 */
async function testUpload() {
  try {
    console.log('🚀 开始测试上传...');
    
    // 检查 public 目录
    const publicDir = path.join(process.cwd(), 'public');
    if (!fs.existsSync(publicDir)) {
      console.error('❌ public 目录不存在');
      return;
    }
    
    console.log('✅ public 目录存在');
    
    // 列出 public 目录内容
    const files = fs.readdirSync(publicDir);
    console.log('📁 public 目录内容:', files);
    
    console.log('🎉 测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testUpload();