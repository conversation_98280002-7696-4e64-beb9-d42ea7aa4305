/**
 * 简单的错误检查脚本
 * 通过 curl 检查应用是否正常启动
 */

const http = require('http');

function checkServer(url = 'http://localhost:3003') {
  return new Promise((resolve, reject) => {
    console.log(`🔍 检查服务器状态: ${url}`);
    
    const request = http.get(url, (response) => {
      let data = '';
      
      response.on('data', (chunk) => {
        data += chunk;
      });
      
      response.on('end', () => {
        if (response.statusCode === 200) {
          console.log('✅ 服务器响应正常');
          console.log(`📊 状态码: ${response.statusCode}`);
          console.log(`📄 内容长度: ${data.length} 字符`);
          
          // 检查是否包含基本的 HTML 结构
          if (data.includes('<html') && data.includes('</html>')) {
            console.log('✅ HTML 结构正常');
            resolve(true);
          } else {
            console.log('❌ HTML 结构异常');
            resolve(false);
          }
        } else {
          console.log(`❌ 服务器响应异常: ${response.statusCode}`);
          resolve(false);
        }
      });
    });
    
    request.on('error', (error) => {
      console.log(`❌ 连接错误: ${error.message}`);
      resolve(false);
    });
    
    request.setTimeout(10000, () => {
      console.log('❌ 请求超时');
      request.destroy();
      resolve(false);
    });
  });
}

async function main() {
  console.log('🚀 开始简单错误检查...');
  
  const isHealthy = await checkServer();
  
  if (isHealthy) {
    console.log('\n✅ 应用运行正常！');
    console.log('💡 请在浏览器中打开 http://localhost:3003 检查是否还有控制台错误');
    process.exit(0);
  } else {
    console.log('\n❌ 应用运行异常！');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { checkServer };
