/**
 * 测试 webpack 错误修复脚本
 * 用于验证应用是否正常运行，没有模块加载错误
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function testWebpackErrors() {
  console.log('🚀 开始测试 webpack 错误修复...');
  
  let browser;
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 监听控制台错误
    const errors = [];
    const warnings = [];
    
    page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'error') {
        errors.push(text);
        console.error('❌ Console Error:', text);
      } else if (type === 'warning') {
        warnings.push(text);
        console.warn('⚠️ Console Warning:', text);
      }
    });
    
    // 监听页面错误
    page.on('pageerror', error => {
      errors.push(error.message);
      console.error('❌ Page Error:', error.message);
    });
    
    // 访问首页
    console.log('📄 正在访问首页...');
    await page.goto('http://localhost:3003', {
      waitUntil: 'networkidle2',
      timeout: 30000
    });
    
    // 等待页面完全加载
    await page.waitForTimeout(3000);
    
    // 检查页面标题
    const title = await page.title();
    console.log('📝 页面标题:', title);
    
    // 检查是否有特定的 webpack 错误
    const webpackErrors = errors.filter(error => 
      error.includes('webpack') || 
      error.includes('Cannot read properties of undefined') ||
      error.includes('reading \'call\'')
    );
    
    // 生成测试报告
    const report = {
      timestamp: new Date().toISOString(),
      title,
      totalErrors: errors.length,
      totalWarnings: warnings.length,
      webpackErrors: webpackErrors.length,
      errors,
      warnings,
      webpackSpecificErrors: webpackErrors,
      status: webpackErrors.length === 0 ? 'PASSED' : 'FAILED'
    };
    
    // 保存报告
    const reportPath = path.join(__dirname, '../log/webpack-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    // 输出结果
    console.log('\n📊 测试结果:');
    console.log(`状态: ${report.status}`);
    console.log(`总错误数: ${report.totalErrors}`);
    console.log(`总警告数: ${report.totalWarnings}`);
    console.log(`Webpack 错误数: ${report.webpackErrors}`);
    
    if (report.status === 'PASSED') {
      console.log('✅ 所有 webpack 错误已修复！');
    } else {
      console.log('❌ 仍有 webpack 错误需要修复:');
      webpackErrors.forEach(error => console.log(`  - ${error}`));
    }
    
    console.log(`📄 详细报告已保存到: ${reportPath}`);
    
    return report.status === 'PASSED';
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    return false;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testWebpackErrors()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = { testWebpackErrors };
