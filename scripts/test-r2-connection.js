/**
 * 测试 R2 连接和权限
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });

const { S3Client, ListBucketsCommand, HeadBucketCommand } = require('@aws-sdk/client-s3');

// Cloudflare R2 配置
const R2_CONFIG = {
  endpoint: `https://${process.env.CLOUDFLARE_ACCOUNT_ID}.r2.cloudflarestorage.com`,
  region: 'auto',
  credentials: {
    accessKeyId: process.env.CLOUDFLARE_R2_ACCESS_KEY_ID,
    secretAccessKey: process.env.CLOUDFLARE_R2_SECRET_ACCESS_KEY,
  },
};

const BUCKET_NAME = process.env.CLOUDFLARE_R2_BUCKET;

console.log('🔍 测试 R2 连接...');
console.log('Endpoint:', R2_CONFIG.endpoint);
console.log('Bucket:', BUCKET_NAME);

const s3Client = new S3Client(R2_CONFIG);

async function testConnection() {
  try {
    console.log('\n1️⃣ 测试基本连接...');
    
    // 测试列出存储桶
    try {
      const listCommand = new ListBucketsCommand({});
      const response = await s3Client.send(listCommand);
      console.log('✅ 连接成功！');
      console.log('📋 可访问的存储桶:', response.Buckets?.map(b => b.Name) || []);
      
      // 检查目标存储桶是否存在
      const bucketExists = response.Buckets?.some(b => b.Name === BUCKET_NAME);
      if (bucketExists) {
        console.log(`✅ 存储桶 "${BUCKET_NAME}" 存在`);
      } else {
        console.log(`❌ 存储桶 "${BUCKET_NAME}" 不存在`);
        console.log('💡 请检查存储桶名称是否正确');
      }
      
    } catch (error) {
      console.error('❌ 连接失败:', error.message);
      console.log('💡 可能的原因:');
      console.log('   - API 令牌权限不足');
      console.log('   - Account ID 不正确');
      console.log('   - API 令牌已过期或无效');
    }
    
    console.log('\n2️⃣ 测试存储桶访问...');
    
    // 测试访问特定存储桶
    try {
      const headCommand = new HeadBucketCommand({ Bucket: BUCKET_NAME });
      await s3Client.send(headCommand);
      console.log(`✅ 可以访问存储桶 "${BUCKET_NAME}"`);
    } catch (error) {
      console.error(`❌ 无法访问存储桶 "${BUCKET_NAME}":`, error.message);
      console.log('💡 可能的原因:');
      console.log('   - 存储桶不存在');
      console.log('   - API 令牌对此存储桶没有权限');
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

testConnection();