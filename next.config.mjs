/** @type {import('next').NextConfig} */
const nextConfig = {
  // 图片优化配置 - 针对 Cloudflare 优化
  images: {
    // 允许外部图片域名
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'cdn.sprunkivaporwave.online', // R2 CDN 域名
      },
      {
        protocol: 'https',
        hostname: '**.r2.cloudflarestorage.com', // R2 直接访问
      },
      {
        protocol: 'https',
        hostname: '**', // 其他外部图片
      },
      {
        protocol: 'http',
        hostname: 'localhost',
      },
    ],
    // 图片格式优化 - Cloudflare 支持的格式
    formats: ['image/webp', 'image/avif'],
    // 启用图片优化
    unoptimized: false,
    // 缓存配置 - 配合 Cloudflare CDN
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30天
    // 设备尺寸 - 针对移动端优化
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    // 图片尺寸
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    // 图片加载器配置 - 仅在构建时使用自定义加载器
    loader: process.env.NEXT_PHASE === 'phase-production-build' ? 'custom' : 'default',
    loaderFile: process.env.NEXT_PHASE === 'phase-production-build' ? './lib/utils/image-loader.js' : undefined,
  },

  // 性能优化
  experimental: {
    // 启用优化包导入
    optimizePackageImports: ['lucide-react', 'react-icons'],
    // 启用Web Vitals
    webVitalsAttribution: ['CLS', 'LCP', 'FID', 'FCP', 'TTFB'],
  },

  // 压缩配置
  compress: true,

  // 静态导出优化
  trailingSlash: false,

  // 重定向配置
  async redirects() {
    return [
      {
        source: '/game/:path*',
        destination: '/:path*',
        permanent: true,
      },
    ];
  },

  // 缓存和安全头配置
  async headers() {
    return [
      {
        // 游戏缩略图缓存 - 长期缓存
        source: '/images/thumbnails/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable', // 1年
          },
        ],
      },
      {
        // 其他静态资源缓存
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=2592000', // 30天
          },
        ],
      },
      {
        // 字体文件缓存
        source: '/:path*.(woff|woff2|eot|ttf|otf)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        // 游戏页面缓存
        source: '/:gameSlug',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=86400', // 1小时客户端，24小时CDN
          },
        ],
      },
      {
        // 安全头配置
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin',
          },
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on',
          },
        ],
      },
    ];
  },

  // Webpack optimization
  webpack: (config, { isServer }) => {
    // Optimize bundle size
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    
    return config;
  },
};

export default nextConfig;
