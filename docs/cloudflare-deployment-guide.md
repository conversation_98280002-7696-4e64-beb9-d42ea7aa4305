# Cloudflare 部署指南

本指南将帮助您将项目完整迁移到 Cloudflare 生态系统，包括 Cloudflare Pages 和 R2 存储。

## 🎯 部署架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Cloudflare 生态架构                        │
├─────────────────────────────────────────────────────────────┤
│  用户请求                                                    │
│      ↓                                                      │
│  Cloudflare CDN (全球边缘节点)                               │
│      ↓                                                      │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │ Cloudflare Pages │    │ Cloudflare R2   │                │
│  │ (Next.js 应用)   │    │ (静态资源)      │                │
│  │ - HTML/CSS/JS    │    │ - 图片          │                │
│  │ - API Routes     │    │ - 字体          │                │
│  │ - SSR/SSG        │    │ - 其他静态文件   │                │
│  └─────────────────┘    └─────────────────┘                │
│           ↓                       ↓                        │
│  Cloudflare Workers      R2 自定义域名                      │
│  (边缘计算)              cdn.sprunkivaporwave.online        │
└─────────────────────────────────────────────────────────────┘
```

## 📋 部署前准备

### 1. Cloudflare 账户设置

1. **注册 Cloudflare 账户**
   - 访问 [Cloudflare](https://cloudflare.com)
   - 注册并验证账户

2. **添加域名**
   - 在 Cloudflare 控制台添加您的域名
   - 更新 DNS 记录指向 Cloudflare

3. **获取必要的 ID 和密钥**
   ```bash
   # 在 Cloudflare 控制台获取以下信息：
   - Account ID (右侧边栏)
   - Zone ID (域名概览页面)
   - R2 API Token (R2 → 管理 R2 API 令牌)
   ```

### 2. 环境变量配置

复制 `.env.cloudflare.example` 为 `.env.local`：

```bash
cp .env.cloudflare.example .env.local
```

填入实际的配置值：

```env
# 基础配置
NEXT_PUBLIC_SITE_URL=https://sprunkivaporwave.online
NEXT_PUBLIC_R2_DOMAIN=https://cdn.sprunkivaporwave.online

# Cloudflare 配置
CLOUDFLARE_ACCOUNT_ID=your_account_id
CLOUDFLARE_R2_BUCKET=sprunkivaporwave-assets
CLOUDFLARE_R2_ACCESS_KEY_ID=your_access_key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your_secret_key
```

## 🚀 部署步骤

### 第一步：创建 R2 存储桶

1. **在 Cloudflare 控制台创建 R2 存储桶**
   ```bash
   存储桶名称: sprunkivaporwave-assets
   区域: 自动
   ```

2. **配置自定义域名**
   - 在 R2 存储桶设置中添加自定义域名
   - 域名: `cdn.sprunkivaporwave.online`
   - 启用 CDN 缓存

3. **设置 CORS 策略**
   ```json
   [
     {
       "AllowedOrigins": ["https://sprunkivaporwave.online"],
       "AllowedMethods": ["GET", "HEAD"],
       "AllowedHeaders": ["*"],
       "MaxAgeSeconds": 3600
     }
   ]
   ```

### 第二步：上传静态资源

1. **安装依赖**
   ```bash
   npm install
   ```

2. **上传现有资源到 R2**
   ```bash
   npm run upload-assets
   ```

3. **验证上传结果**
   - 检查 R2 控制台中的文件
   - 测试 CDN 域名访问

### 第三步：部署到 Cloudflare Pages

1. **连接 Git 仓库**
   - 在 Cloudflare Pages 控制台
   - 选择 "连接到 Git"
   - 选择您的仓库

2. **配置构建设置**
   ```yaml
   构建命令: npm run cf:build
   构建输出目录: .next
   根目录: /
   环境变量: 从 .env.cloudflare.example 复制
   ```

3. **设置自定义域名**
   - 在 Pages 项目设置中添加自定义域名
   - 配置 DNS 记录

### 第四步：优化和监控

1. **启用缓存规则**
   ```javascript
   // 在 Cloudflare 控制台设置页面规则
   *.sprunkivaporwave.online/images/*
   - 缓存级别: 缓存所有内容
   - 边缘缓存 TTL: 1 个月
   
   *.sprunkivaporwave.online/*
   - 缓存级别: 标准
   - 浏览器缓存 TTL: 4 小时
   ```

2. **配置安全设置**
   - 启用 SSL/TLS 加密
   - 设置安全级别为 "中等"
   - 启用 Bot Fight Mode

## 🔧 开发工作流

### 本地开发

```bash
# 启动开发服务器
npm run dev

# 测试构建
npm run build
npm run start
```

### 部署流程

```bash
# 方式一：自动部署（推荐）
git push origin main  # Pages 会自动构建和部署

# 方式二：手动部署
npm run deploy:cloudflare
```

### 资源更新

```bash
# 上传新的静态资源
npm run upload-assets

# 清除 CDN 缓存
# 在 Cloudflare 控制台 → 缓存 → 清除缓存
```

## 📊 性能优化

### 1. 图片优化

- 使用 `OptimizedImage` 组件
- 自动 WebP/AVIF 格式转换
- 响应式图片加载
- 懒加载和预加载

### 2. 缓存策略

```javascript
// 缓存配置
图片资源: 1 年 (immutable)
字体文件: 1 年 (immutable)
CSS/JS: 1 年 (immutable)
HTML: 1 小时 (可更新)
```

### 3. CDN 优化

- 全球边缘节点缓存
- 智能路由
- HTTP/3 支持
- Brotli 压缩

## 🔍 监控和分析

### 1. Cloudflare Analytics

- 访问 Cloudflare 控制台查看流量统计
- 监控缓存命中率
- 查看安全威胁拦截

### 2. 性能监控

```javascript
// 在组件中使用性能监控
import { PerformanceMonitor } from '@/components/common/PerformanceMonitor';

// 监控 Core Web Vitals
- LCP (Largest Contentful Paint)
- FID (First Input Delay)
- CLS (Cumulative Layout Shift)
```

### 3. 错误追踪

- 使用 Cloudflare Workers 进行错误日志收集
- 集成第三方错误追踪服务

## 💰 成本优化

### Cloudflare 定价优势

```
Cloudflare Pages:
- 免费: 500 次构建/月, 无限带宽
- Pro: $20/月, 5000 次构建/月

Cloudflare R2:
- 存储: $0.015/GB/月
- 出站流量: 免费 (通过 CDN)
- 操作: $4.50/百万次请求

对比 Vercel:
- 带宽节省: 80%+
- 存储成本: 60%+
- 总体节省: 70%+
```

## 🚨 故障排除

### 常见问题

1. **图片无法加载**
   ```bash
   # 检查 R2 域名配置
   # 验证 CORS 设置
   # 确认文件上传成功
   ```

2. **构建失败**
   ```bash
   # 检查环境变量
   # 验证 Node.js 版本
   # 查看构建日志
   ```

3. **缓存问题**
   ```bash
   # 清除 Cloudflare 缓存
   # 检查缓存规则配置
   # 验证 Cache-Control 头
   ```

### 获取帮助

- [Cloudflare 文档](https://developers.cloudflare.com/)
- [Next.js 部署指南](https://nextjs.org/docs/deployment)
- [项目 GitHub Issues](https://github.com/your-repo/issues)

## 🎉 部署完成

恭喜！您的项目现在已经成功部署到 Cloudflare 生态系统。享受更快的加载速度和更低的成本吧！

### 验证清单

- [ ] 网站可以正常访问
- [ ] 图片资源正常加载
- [ ] CDN 缓存正常工作
- [ ] SSL 证书有效
- [ ] 性能指标良好
- [ ] 监控和分析正常

### 下一步

- 配置自动化部署流程
- 设置监控告警
- 优化 SEO 设置
- 添加更多性能优化