/**
 * 图片测试页面
 * 用于验证 Cloudflare R2 图片加载是否正常
 */

import OptimizedImage, { GameThumbnail } from '@/components/common/OptimizedImage';

export default function TestImagesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">图片加载测试</h1>
      
      <div className="space-y-8">
        {/* 测试游戏缩略图 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">游戏缩略图测试</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <GameThumbnail
              src="/images/thumbnails/pokemon-gamma-emerald.png"
              alt="Pokemon Gamma Emerald"
              priority={true}
            />
            <GameThumbnail
              src="/images/thumbnails/pokemon-gamma-emerald-img.png"
              alt="Pokemon Gamma Emerald Screenshot"
            />
          </div>
        </section>

        {/* 测试普通图片 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">普通图片测试</h2>
          <OptimizedImage
            src="/og.png"
            alt="Open Graph Image"
            width={600}
            height={315}
            className="rounded-lg shadow-lg"
          />
        </section>

        {/* 测试图标 */}
        <section>
          <h2 className="text-2xl font-semibold mb-4">图标测试</h2>
          <div className="flex gap-4">
            <OptimizedImage
              src="/favicon-32x32.png"
              alt="Favicon 32x32"
              width={32}
              height={32}
            />
            <OptimizedImage
              src="/android-chrome-192x192.png"
              alt="Android Chrome 192x192"
              width={64}
              height={64}
            />
          </div>
        </section>
      </div>

      {/* 加载状态说明 */}
      <div className="mt-12 p-6 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
        <h3 className="text-lg font-semibold mb-2">测试说明</h3>
        <ul className="list-disc list-inside space-y-1 text-sm">
          <li>如果图片正常显示，说明 R2 配置成功</li>
          <li>图片应该有加载动画效果</li>
          <li>在开发环境下，图片从本地 public 目录加载</li>
          <li>在生产环境下，图片从 Cloudflare R2 加载</li>
        </ul>
      </div>
    </div>
  );
}