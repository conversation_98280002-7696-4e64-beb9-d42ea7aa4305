import GoogleAnalytics from "@/app/GoogleAnalytics";
import GoogleAdSense from "@/app/GoogleAdSense";
import MicrosoftClarity from "@/app/MicrosoftClarity";
import Footer from '@/components/layout/footer/Footer';
import Header from '@/components/layout/header/Header';
import Sidebar, { SidebarProvider } from '@/components/layout/Sidebar';
import MainContent from '@/components/layout/MainContent';
import ErrorBoundary from '@/components/common/ErrorBoundary';
import StructuredData from '@/components/common/StructuredData';
import PerformanceMonitor from '@/components/common/PerformanceMonitor';
import { siteConfig } from "@/config/site";
import { generateOrganizationSchema } from '@/lib/constants/structured-data';
import { cn } from '@/lib/utils/utils';
import "@/styles/globals.css";
import "@/styles/loading.css";
import "@/styles/adsense.css";
import { Analytics } from "@vercel/analytics/react";
import { Viewport } from "next";
import { ThemeProvider } from "next-themes";
import { StagewiseToolbar } from '@stagewise/toolbar-next';
import ReactPlugin from '@stagewise-plugins/react';

export const metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
  authors: siteConfig.authors,
  creator: siteConfig.creator,
  icons: siteConfig.icons,
  metadataBase: siteConfig.metadataBase,
  openGraph: siteConfig.openGraph,
  twitter: siteConfig.twitter,
};
export const viewport: Viewport = {
  themeColor: siteConfig.themeColors,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 生成网站级别的结构化数据
  const organizationSchema = generateOrganizationSchema();

  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body className={cn("min-h-screen bg-background font-sans antialiased")}>
        {/* 网站级别结构化数据 */}
        <StructuredData data={organizationSchema} />
        
        <ThemeProvider
          attribute="class"
          defaultTheme={siteConfig.defaultNextTheme}
          enableSystem
          disableTransitionOnChange
        >
          <SidebarProvider>
            <ErrorBoundary>
              <Header />
              <Sidebar />
              <MainContent>
                {children}
              </MainContent>
              <Footer />
            </ErrorBoundary>
          </SidebarProvider>
          <Analytics />
          
          {/* Stagewise Toolbar - only in development and when explicitly enabled */}
          {process.env.NODE_ENV === "development" && process.env.ENABLE_STAGEWISE === "true" && (
            <StagewiseToolbar
              config={{
                plugins: [ReactPlugin],
              }}
            />
          )}
        </ThemeProvider>
        {/* 性能监控 */}
        <PerformanceMonitor 
          enabled={process.env.NODE_ENV === "production"}
          sendToGA={true}
          debug={process.env.NODE_ENV === "development"}
        />
        
        {process.env.NODE_ENV === "development" ? (
          <></>
        ) : (
          <>
            <GoogleAnalytics />
            <GoogleAdSense />
            <MicrosoftClarity />
          </>
        )}
      </body>
    </html>
  );
}
