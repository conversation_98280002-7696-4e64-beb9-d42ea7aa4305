# Cloudflare 部署完整操作指南

## 📋 当前进度检查

### ✅ 已完成的步骤
- [x] 注册 Cloudflare 账户
- [x] 获取 Account ID: `1fdd139c31307d10efb1f4e64d48d34c`
- [x] 创建 R2 API 令牌
- [x] 创建 R2 存储桶: `games-igm`
- [x] 配置自定义域名: `assets.sprunkivaporwave.online`
- [x] 配置环境变量文件 `.env.local`
- [x] 更新项目代码以支持 R2

### 🎯 接下来需要完成的步骤

---

## 第四阶段：手动上传静态资源（约10分钟）

### 步骤 4：上传文件到 R2 存储桶

1. **登录 Cloudflare 控制台**
   - 访问 https://dash.cloudflare.com
   - 进入 "R2 对象存储"
   - 点击 `games-igm` 存储桶

2. **创建项目文件夹**
   - 在存储桶中点击 "创建文件夹"
   - 文件夹名称：`sprunkivaporwave`

3. **上传文件**
   - 进入 `sprunkivaporwave` 文件夹
   - 将您项目 `public/` 目录下的所有文件上传到这里
   - 需要上传的文件：
     ```
     ├── images/
     │   └── thumbnails/
     │       ├── pokemon-gamma-emerald.png
     │       ├── pokemon-gamma-emerald-img.png
     │       └── pokemon-gammae-merald-banner-controls.png
     ├── android-chrome-192x192.png
     ├── favicon-16x16.png
     ├── favicon-32x32.png
     ├── favicon.ico
     └── og.png
     ```

4. **验证上传**
   - 确认所有文件都在 `games-igm/sprunkivaporwave/` 路径下
   - 测试访问一个文件：`https://assets.sprunkivaporwave.online/images/thumbnails/pokemon-gamma-emerald.png`

---

## 第五阶段：本地测试（约10分钟）

### 步骤 5：测试图片组件

1. **启动开发服务器**
   ```bash
   npm run dev
   ```

2. **访问测试页面**
   - 打开浏览器访问：`http://localhost:3000/test-images`
   - 检查图片是否正常显示
   - 检查浏览器控制台是否有错误

3. **验证图片加载**
   - 在开发环境下，图片应该从本地 `public/` 目录加载
   - 图片应该有加载动画效果
   - 所有图片都应该正常显示

---

## 第六阶段：准备部署（约5分钟）

### 步骤 6：提交代码到 Git

1. **检查更改**
   ```bash
   git status
   ```

2. **添加所有文件**
   ```bash
   git add .
   ```

3. **提交更改**
   ```bash
   git commit -m "配置 Cloudflare R2 和图片优化组件"
   ```

4. **推送到远程仓库**
   ```bash
   git push origin main
   ```

---

## 第七阶段：部署到 Cloudflare Pages（约15分钟）

### 步骤 7：创建 Cloudflare Pages 项目

1. **在 Cloudflare 控制台**
   - 左侧菜单点击 "Pages"
   - 点击 "创建项目" 或 "Create a project"
   - 选择 "连接到 Git" 或 "Connect to Git"

2. **连接 Git 仓库**
   - 选择您的 Git 提供商（GitHub/GitLab）
   - 授权 Cloudflare 访问您的仓库
   - 选择您的项目仓库

3. **配置构建设置**
   ```
   项目名称: sprunkivaporwave-online
   生产分支: main
   构建命令: npm run build
   构建输出目录: .next
   根目录: (留空)
   ```

### 步骤 8：配置环境变量

在 Pages 项目的环境变量部分添加以下变量：

```
NEXT_PUBLIC_SITE_URL = https://sprunkivaporwave.online
NEXT_PUBLIC_SITE_NAME = Sprunki Vaporwave Online
NEXT_PUBLIC_R2_DOMAIN = https://assets.sprunkivaporwave.online
CLOUDFLARE_ACCOUNT_ID = 1fdd139c31307d10efb1f4e64d48d34c
CLOUDFLARE_R2_BUCKET = games-igm
NEXT_PUBLIC_R2_PROJECT_PATH = sprunkivaporwave
CLOUDFLARE_R2_ACCESS_KEY_ID = ****************************************
CLOUDFLARE_R2_SECRET_ACCESS_KEY = ****************************************
NODE_ENV = production
NEXT_TELEMETRY_DISABLED = 1
```

### 步骤 9：开始部署

1. **保存并部署**
   - 点击 "保存并部署" 或 "Save and Deploy"
   - 等待构建完成（通常需要3-5分钟）

2. **查看构建日志**
   - 如果构建失败，查看详细的错误日志
   - 常见问题：环境变量配置错误、依赖安装失败

---

## 第八阶段：配置自定义域名（约10分钟）

### 步骤 10：为 Pages 添加自定义域名

1. **在 Pages 项目中**
   - 点击 "自定义域名" 或 "Custom domains"
   - 点击 "设置自定义域名" 或 "Set up a custom domain"
   - 输入：`sprunkivaporwave.online`

2. **配置 DNS 记录**
   - 如果域名已在 Cloudflare 管理，会自动添加记录
   - 如果不在，需要手动添加 CNAME 记录指向 Pages 项目

3. **等待 SSL 证书**
   - Cloudflare 会自动为您的域名颁发 SSL 证书
   - 通常需要几分钟到几小时

---

## 第九阶段：验证部署（约10分钟）

### 步骤 11：验证网站功能

1. **访问主网站**
   - 打开 `https://sprunkivaporwave.online`
   - 确认网站正常加载

2. **检查图片加载**
   - 访问 `https://sprunkivaporwave.online/test-images`
   - 确认图片从 R2 CDN 正常加载
   - 在浏览器开发者工具中检查图片请求来源

3. **检查性能**
   - 使用浏览器开发者工具检查加载速度
   - 确认缓存头设置正确
   - 检查 Core Web Vitals 指标

### 步骤 12：清理和优化

1. **删除测试页面**
   ```bash
   rm -rf app/test-images
   git add .
   git commit -m "删除测试页面"
   git push origin main
   ```

2. **配置缓存规则（可选）**
   - 在 Cloudflare 域名管理中设置页面规则
   - 为静态资源设置更长的缓存时间

---

## 🎉 部署完成检查清单

- [ ] R2 存储桶创建并配置完成
- [ ] 静态资源已上传到 R2
- [ ] 本地测试图片组件正常
- [ ] 代码已提交到 Git 仓库
- [ ] Cloudflare Pages 项目创建成功
- [ ] 环境变量配置正确
- [ ] 自定义域名配置完成
- [ ] SSL 证书生效
- [ ] 网站可以正常访问
- [ ] 图片从 R2 CDN 正常加载
- [ ] 性能指标良好

---

## 🆘 如果遇到问题

### 常见问题解决

1. **构建失败**
   - 检查环境变量配置
   - 查看构建日志中的具体错误
   - 确认 Node.js 版本兼容性

2. **图片无法加载**
   - 检查 R2 存储桶的公共访问设置
   - 确认自定义域名配置正确
   - 验证文件路径和文件名

3. **域名无法访问**
   - 检查 DNS 记录配置
   - 等待 DNS 传播（可能需要几小时）
   - 确认 SSL 证书状态

4. **性能问题**
   - 检查缓存设置
   - 优化图片格式和大小
   - 启用 Cloudflare 的性能优化功能

---

## 📞 获取帮助

如果在任何步骤遇到问题，请告诉我：
1. 您当前在哪个步骤
2. 遇到的具体错误信息
3. 您看到的页面截图（如果需要）

我会帮您逐步解决问题！