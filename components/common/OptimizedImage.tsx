/**
 * 优化的图片组件
 * 专为 Cloudflare R2 + CDN 设计
 */

'use client';

import Image from 'next/image';
import { useState } from 'react';
import { getR2AssetUrl, generateSrcSet } from '@/lib/utils/cloudflare';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  sizes?: string;
  quality?: number;
  format?: 'webp' | 'avif' | 'png' | 'jpg';
  fallback?: string;
  loading?: 'lazy' | 'eager';
  onLoad?: () => void;
  onError?: () => void;
}

/**
 * 优化的图片组件
 * 自动处理 R2 URL、格式转换、响应式图片和错误回退
 */
export default function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 85,
  format = 'webp',
  fallback,
  loading = 'lazy',
  onLoad,
  onError,
}: OptimizedImageProps) {
  const [imageError, setImageError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 处理图片加载错误
  const handleError = () => {
    setImageError(true);
    setIsLoading(false);
    onError?.();
  };

  // 处理图片加载完成
  const handleLoad = () => {
    setIsLoading(false);
    onLoad?.();
  };

  // 如果有错误且有回退图片，使用回退图片
  const imageSrc = imageError && fallback ? fallback : src;
  
  // 生成优化的图片 URL
  const optimizedSrc = getR2AssetUrl(imageSrc, {
    width,
    height,
    format,
    quality,
  });

  // 生成响应式 srcSet
  const srcSet = width ? generateSrcSet(imageSrc) : undefined;

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {/* 加载状态 */}
      {isLoading && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse rounded" />
      )}
      
      {/* 优化的图片 */}
      <Image
        src={optimizedSrc}
        alt={alt}
        width={width}
        height={height}
        className={`transition-opacity duration-300 ${
          isLoading ? 'opacity-0' : 'opacity-100'
        }`}
        priority={priority}
        loading={loading}
        sizes={sizes}
        srcSet={srcSet}
        quality={quality}
        onLoad={handleLoad}
        onError={handleError}
        // 添加图片优化属性
        placeholder="blur"
        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
      />
      
      {/* 错误状态 */}
      {imageError && !fallback && (
        <div className="absolute inset-0 bg-gray-100 dark:bg-gray-900 flex items-center justify-center">
          <div className="text-gray-400 text-sm">图片加载失败</div>
        </div>
      )}
    </div>
  );
}

/**
 * 游戏缩略图组件
 * 专门用于游戏缩略图的优化显示
 */
export function GameThumbnail({
  src,
  alt,
  className = '',
  priority = false,
}: {
  src: string;
  alt: string;
  className?: string;
  priority?: boolean;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={400}
      height={300}
      className={`rounded-lg ${className}`}
      priority={priority}
      sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 400px"
      format="webp"
      quality={90}
      fallback="/images/placeholder-game.png"
    />
  );
}

/**
 * 横幅图片组件
 * 用于页面顶部的横幅图片
 */
export function BannerImage({
  src,
  alt,
  className = '',
}: {
  src: string;
  alt: string;
  className?: string;
}) {
  return (
    <OptimizedImage
      src={src}
      alt={alt}
      width={1200}
      height={400}
      className={`w-full ${className}`}
      priority={true}
      sizes="100vw"
      format="webp"
      quality={85}
    />
  );
}