{"name": "nextjs-i18n-starter", "version": "2.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "add-games": "node scripts/create-games.js", "add-games-basic": "node scripts/create-games.js --no-ai", "add-games-enhanced": "node scripts/create-games-enhanced.js", "add-games-enhanced-basic": "node scripts/create-games-enhanced.js --no-ai", "add-games-quality": "node scripts/create-games-enhanced.js --batch-size=2", "test-ai": "node scripts/test-ai-connection.js", "validate-yaml": "node scripts/validate-yaml.js", "clean": "rm -rf node_modules .next", "upload-assets": "node scripts/upload-to-r2.js", "test-env": "node scripts/test-env.js", "upload-debug": "node scripts/upload-to-r2-debug.js", "test-r2": "node scripts/test-r2-connection.js", "test-urls": "node scripts/test-image-urls.js", "deploy:cloudflare": "npm run build && npm run upload-assets", "cf:build": "chmod +x cloudflare/pages-build.sh && ./cloudflare/pages-build.sh"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.4.1", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "gray-matter": "^4.0.3", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.468.0", "next": "15.4.1", "next-themes": "^0.4.4", "react": "19.1.0", "react-dom": "19.1.0", "react-icons": "^5.4.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "web-vitals": "^5.0.3", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "@aws-sdk/client-s3": "^3.490.0", "mime-types": "^2.1.35"}, "devDependencies": {"@stagewise-plugins/react": "^0.6.2", "@stagewise/toolbar-next": "^0.6.2", "@types/node": "^20", "@types/react": "^19.0.0", "@types/react-dom": "^19.0.0", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "15.4.1", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5"}, "pnpm": {"overrides": {"next": "15.4.1", "react": "19.1.0", "react-dom": "19.1.0"}}}